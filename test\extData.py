from vnstock import Vnstock
import pandas as pd
from datetime import datetime, timedelta
import os
import time

STOCK_SECTORS = {
    "Tài chính": ["VCB", "BID", "TCB", "CTG", "MBB", "VPB", "LPB", "ACB", "STB", "HDB", "SHB", "VIB", "SSB", "SSI", "EIB", "BVH", "TPB", "MSB", "OCB", "VCI", "VND", "NAB", "VIX", "HCM", "FUEVFVND", "FTS", "BSI", "EVF", "DSE", "E1VFVN30", "BIC","CTS","VDS","MIG","DSC","AGR","ORS","TVS","APG","BMI","PGI", "FUEKIV30"],
    "Bất động sản": ["VIC", "VHM", "VRE", "KDH", "NVL", "BCM", "KBC", "NLG", "DXG", "VPI", "SIP", "DIG", "CII", "SZC", "DXS", "IJC", "HDC", "CRE", "QCG", "SCR", "AGG", "KHG", "NBB", "NTL", "SGR", "HQC", "CKG", "HPX", "ITC", "LHG", "TIP", "EVG", "TDC", "SZL", "TIX", "D2D"],
    "Tiêu dùng không thiết yếu": ["VPL", "MWG", "PLX", "GEE", "PNJ", "FRT", "DGW", "HHS", "TCM", "MSH", "STK", "PET", "DRC", "CTF","GIL", "HAX", "HTG", "CSM","TTF", "SVC"],
    "Tiêu dùng thiết yếu": ["VNM", "MSN", "SAB", "SBT", "HAG", "VHC", "KDC", "BAF", "DBC", "BHN", "VCF", "PAN", "ANV", "MCM", "ASM", "FMC", "NAF", "NSC", "LIX", "CLC", "OGC", "IDI", "SMB", "BBC"],
    "Nguyên vật liệu": ["HPG", "GVR", "DGC", "DCM", "DPM","HSG", "PHR", "NKG", "CSV", "ACG", "HT1", "PTB", "DPR", "TDP", "AAA", "VFG", "BFC", "DHC", "KSB", "THG", "TRC", "APH", "LBM", "CVT"],
    "Công nghệ thông tin": ["FPT", "CMG", "ELC"],
    "Dịch vụ viễn thông": ["CTR", "SGT", "YEG"],
    "Dịch vụ tiện ích": ["GAS", "REE", "POW", "PGV", "VSH", "BWE", "HDG", "KOS", "GEG", "TDM", "HNA", "NT2", "CHP", "TMP", "PPC", "SHP", "PGD", "VPD", "TBC", "TTA", "SBA", "S4A", "TTE", "SJD"],
    "Năng lượng": ["BSR", "PVD", "PVT", "PVP"],
    "Công nghiệp": ["HVN", "VJC", "GEX", "GMD", "VGC", "VTP", "VCG", "LGC", "BMP", "HAH", "TMS", "PDN", "STG", "DVP", "AST", "PC1", "SCS", "VSC", "TLG", "NCT", "TV2", "BCG", "SHI", "SGN", "VOS", "RAL", "FCN", "PAC", "ASG", "CTD", "HHV", "DPG", "SAM", "LCG", "ACC", "QNP", "DC4", "CLL", "ILEI", "CTI", "TCL",],
    "Chăm sóc sức khỏe": ["DHG", "IMP", "DBD", "TRA", "TNH", "DMC", "DCL", "OPC", "FIT"]
}

# Chuyển đổi tên ngành sang mã ngành ngắn gọn
SECTOR_CODES = {
    "Tài chính": "FIN",
    "Bất động sản": "REAL",
    "Tiêu dùng không thiết yếu": "CONS",
    "Tiêu dùng thiết yếu": "NECS",
    "Nguyên vật liệu": "MAT",
    "Công nghệ thông tin": "IT",
    "Dịch vụ viễn thông": "TELE",
    "Dịch vụ tiện ích": "UTIL",
    "Năng lượng": "ENER",
    "Công nghiệp": "IND",
    "Chăm sóc sức khỏe": "HEAL"
}

def get_stock_historical_data(symbol, start_date, end_date, interval='1D', source='VCI'):
    """
    Lấy dữ liệu lịch sử giao dịch của một mã cổ phiếu
    
    Args:
        symbol (str): Mã cổ phiếu
        start_date (str): Ngày bắt đầu theo định dạng 'YYYY-MM-DD'
        end_date (str): Ngày kết thúc theo định dạng 'YYYY-MM-DD'
        interval (str): Khoảng thời gian ('1D', '1W', '1M')
        source (str): Nguồn dữ liệu ('VCI', 'TCBS', 'SSI', 'VNDIRECT')
        
    Returns:
        pd.DataFrame: DataFrame chứa dữ liệu lịch sử
    """
    try:
        # Khởi tạo đối tượng Vnstock
        stock = Vnstock().stock(symbol=symbol, source=source)
        
        # Lấy dữ liệu lịch sử
        historical_data = stock.quote.history(start=start_date, end=end_date, interval=interval)
        
        # Đảm bảo cột thời gian có định dạng đúng
        if 'time' in historical_data.columns:
            if not pd.api.types.is_datetime64_any_dtype(historical_data['time']):
                try:
                    historical_data['time'] = pd.to_datetime(historical_data['time'])
                except:
                    pass
        
        return historical_data
    except Exception as e:
        print(f"Lỗi khi lấy dữ liệu cho {symbol}: {str(e)}")
        return pd.DataFrame()  # Trả về DataFrame rỗng nếu có lỗi

def save_data_to_excel(data, symbol, sector_code, start_date_str, end_date_str):
    """
    Lưu dữ liệu vào file Excel
    
    Args:
        data (pd.DataFrame): DataFrame chứa dữ liệu
        symbol (str): Mã cổ phiếu
        sector_code (str): Mã ngành
        start_date_str (str): Ngày bắt đầu theo định dạng 'DDMMYYYY'
        end_date_str (str): Ngày kết thúc theo định dạng 'DDMMYYYY'
    """
    if data.empty:
        print(f"Không có dữ liệu để lưu cho {symbol}")
        return
    
    # Tạo thư mục data nếu chưa tồn tại
    os.makedirs("data", exist_ok=True)
    
    # Tạo tên file theo định dạng yêu cầu
    filename = f"data/{symbol}_{sector_code}_{start_date_str}-{end_date_str}.xlsx"
    
    # Tạo một bản sao của DataFrame để tránh thay đổi dữ liệu gốc
    df_to_save = data.copy()
    
    # Chuyển đổi cột thời gian sang chuỗi nếu là datetime
    if 'time' in df_to_save.columns and pd.api.types.is_datetime64_any_dtype(df_to_save['time']):
        df_to_save['time'] = df_to_save['time'].dt.strftime('%Y-%m-%d')
    
    # Lưu DataFrame vào Excel
    try:
        df_to_save.to_excel(filename, index=False)
        print(f"Đã lưu dữ liệu vào file '{filename}'")
    except Exception as e:
        print(f"Lỗi khi lưu file {filename}: {str(e)}")

def main():
    # Định nghĩa khoảng thời gian
    start_date = "2023-06-10"
    end_date = "2025-06-10"
    
    # Chuyển đổi định dạng ngày cho tên file
    start_date_str = "10062023"
    end_date_str = "10062025"
    
    # Tạo thư mục data nếu chưa tồn tại
    os.makedirs("data", exist_ok=True)
    
    # Tạo file log với mã hóa UTF-8
    log_file = open("data/download_log.txt", "w", encoding="utf-8")
    log_file.write(f"Bắt đầu tải dữ liệu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    log_file.write(f"Khoảng thời gian: {start_date} đến {end_date}\n\n")
    
    # Đếm số lượng mã cổ phiếu đã xử lý
    total_stocks = sum(len(stocks) for stocks in STOCK_SECTORS.values())
    processed_stocks = 0
    
    # Lặp qua từng ngành và mã cổ phiếu
    for sector, stocks in STOCK_SECTORS.items():
        sector_code = SECTOR_CODES[sector]
        log_file.write(f"\nNgành: {sector} ({sector_code})\n")
        
        for symbol in stocks:
            try:
                print(f"Đang lấy dữ liệu cho {symbol} ({sector})...")
                log_file.write(f"- {symbol}: ")
                
                # Lấy dữ liệu
                data = get_stock_historical_data(symbol, start_date, end_date)
                
                if not data.empty:
                    # Lưu dữ liệu vào file Excel
                    save_data_to_excel(data, symbol, sector_code, start_date_str, end_date_str)
                    log_file.write(f"Thành công ({len(data)} bản ghi)\n")
                else:
                    log_file.write("Không có dữ liệu\n")
                
                # Tăng số lượng mã đã xử lý
                processed_stocks += 1
                print(f"Tiến độ: {processed_stocks}/{total_stocks} ({processed_stocks/total_stocks*100:.1f}%)")
                
                # Tạm dừng để tránh quá tải API
                time.sleep(1)
                
            except Exception as e:
                error_msg = f"Lỗi khi xử lý {symbol}: {str(e)}"
                print(error_msg)
                log_file.write(f"Lỗi: {str(e)}\n")
    
    log_file.write(f"\nKết thúc tải dữ liệu: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    log_file.write(f"Đã xử lý: {processed_stocks}/{total_stocks} mã cổ phiếu")
    log_file.close()
    
    print(f"\nĐã hoàn thành tải dữ liệu cho {processed_stocks}/{total_stocks} mã cổ phiếu.")
    print(f"Xem chi tiết trong file log: data/download_log.txt")

if __name__ == "__main__":
    main()