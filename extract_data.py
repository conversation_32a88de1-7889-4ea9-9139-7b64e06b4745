import pandas as pd
from bs4 import BeautifulSoup
import glob
import os

def extract_data_from_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
        soup = BeautifulSoup(content, 'html.parser')
    rows = soup.find_all('tr', class_=['oddOwner', 'evenOwner'])
    dates = []
    closing_prices = []
    for row in rows:
        date = row.find('td', class_='owner_time').text.strip()
        close_price = row.find_all('td', class_='owner_priceClose')[0].text.strip()
        dates.append(date)
        closing_prices.append(close_price)
    return dates, closing_prices

def main():
    # Tạo danh sách để lưu tất cả dữ liệu
    all_data = []
    
    # Đọc tất cả các file XLS trong thư mục Data
    for file_path in glob.glob('Data/*.xls'):
        file_number = os.path.basename(file_path).split('.')[0]
        dates, prices = extract_data_from_file(file_path)
        
        # Thêm dữ liệu vào danh sách
        for date, price in zip(dates, prices):
            all_data.append({
                'File': file_number,
                'Ngày': date,
                'Giá đóng cửa': price
            })
    
    # Tạo DataFrame từ dữ liệu đã thu thập
    df = pd.DataFrame(all_data)
    
    # Sắp xếp theo ngày
    df['Ngày'] = pd.to_datetime(df['Ngày'], format='%d/%m/%Y')
    df = df.sort_values('Ngày')
    df['Ngày'] = df['Ngày'].dt.strftime('%d/%m/%Y')
    
    # Lưu vào file Excel mới
    df.to_excel('ket_qua_tong_hop.xlsx', index=False)
    print("Đã lưu dữ liệu vào file 'ket_qua_tong_hop.xlsx'")

if __name__ == "__main__":
    main()