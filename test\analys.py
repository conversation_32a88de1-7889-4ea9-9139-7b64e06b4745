import pandas as pd
import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
import seaborn as sns
import os
import warnings
warnings.filterwarnings("ignore")

# 1. <PERSON><PERSON><PERSON> và tổng hợp dữ liệu từ các file Excel
data_dir = "data/"  # Thư mục chứa file Excel
prices_dict = {}
industry_dict = {}  # Lưu ngành của mỗi mã

for file_name in os.listdir(data_dir):
    if file_name.endswith(".xlsx"):
        df = pd.read_excel(os.path.join(data_dir, file_name))
        stock_code = file_name.split("_")[0]  # Ví dụ: FPT
        industry = file_name.split("_")[1]  # Ví dụ: IT
        industry_dict[stock_code] = industry
        prices_dict[stock_code] = pd.Series(
            df.iloc[1:499, 4].values,  # Cột E (E2:E499)
            index=pd.to_datetime(df.iloc[1:499, 0])  # Cột A (A2:A499)
        )

# Tạo DataFrame giá đóng cửa
prices = pd.DataFrame(prices_dict)
prices.index.name = "Date"
prices.to_csv("prices_all_stocks.csv")
print(f"Tổng hợp {prices.shape[1]} mã cổ phiếu, {prices.shape[0]} ngày.")

# 2. Lọc mã thiếu dữ liệu (<448 ngày)
min_days = 448
valid_stocks = prices.count() >= min_days
prices = prices.loc[:, valid_stocks]
industry_dict = {k: v for k, v in industry_dict.items() if k in prices.columns}
print(f"Số mã sau lọc: {prices.shape[1]}")
prices.to_csv("filtered_prices.csv")

# 3. Tính lợi suất
returns = np.log(prices / prices.shift(1)).dropna()
returns.to_csv("returns.csv")

# 4. Tính ma trận tương quan
correlation_matrix = returns.corr(method="pearson")
correlation_matrix.to_csv("correlation_matrix.csv")

# 5. Xây dựng mạng lưới
theta = 0.55
corr_array = np.abs(correlation_matrix.to_numpy())
adj_array = (corr_array >= theta).astype(float) * corr_array
np.fill_diagonal(adj_array, 0)
adj_df = pd.DataFrame(adj_array, index=correlation_matrix.index, columns=correlation_matrix.columns)
adj_df.to_csv("adj_matrix.csv")

# 6. Tạo mạng lưới
G = nx.from_pandas_adjacency(adj_df)

# 7. Tính các thước đo độ trung tâm
degree_centrality = nx.degree_centrality(G)
weighted_degree = {n: sum(d["weight"] for _, d in G[n].items()) for n in G.nodes()}
betweenness = nx.betweenness_centrality(G, weight="weight")
closeness = nx.closeness_centrality(G, distance="weight")
eigenvector = nx.eigenvector_centrality(G, weight="weight", max_iter=1000)

centrality_df = pd.DataFrame({
    "Degree": degree_centrality,
    "Weighted_Degree": weighted_degree,
    "Betweenness": betweenness,
    "Closeness": closeness,
    "Eigenvector": eigenvector
}, index=adj_df.index)
centrality_df.to_csv("centrality_measures.csv")

# 8. Thực hiện các đánh giá

# a. Đánh giá cấu trúc mạng lưới
num_nodes = G.number_of_nodes()
num_edges = G.number_of_edges()
print(f"\nĐánh giá cấu trúc mạng lưới:")
print(f"Số nút (cổ phiếu): {num_nodes}")
print(f"Số cạnh (tương quan): {num_edges}")
print(f"Tỷ lệ cạnh/nút: {num_edges/num_nodes:.2f}")

# Vẽ phân bố Weighted Degree (Hình 2)
plt.figure(figsize=(8, 6))
sns.histplot(centrality_df["Weighted_Degree"], bins=30, kde=True, color="blue")
plt.title("Distribution of Weighted Degree Centrality")
plt.xlabel("Weighted Degree")
plt.ylabel("Frequency")
plt.savefig("degree_distribution.png")
plt.close()

# b. Đánh giá thước đo độ trung tâm
print("\nTop 10 cổ phiếu theo Weighted Degree:")
top10_weighted_degree = centrality_df[["Weighted_Degree"]].sort_values(by="Weighted_Degree", ascending=False).head(10)
print(top10_weighted_degree)

print("\nTop 10 cổ phiếu theo các thước đo khác:")
for metric in ["Degree", "Betweenness", "Closeness", "Eigenvector"]:
    top10 = centrality_df[[metric]].sort_values(by=metric, ascending=False).head(10)
    print(f"\nTop 10 theo {metric}:\n{top10}")

# Vẽ tương quan giữa các thước đo (Hình 3)
sns.pairplot(centrality_df[["Degree", "Betweenness", "Closeness", "Eigenvector"]], diag_kind="hist", corner=True)
plt.suptitle("Correlations between Centrality Measures", y=1.02)
plt.savefig("centrality_correlations.png")
plt.close()

# c. Đánh giá vai trò ngành
centrality_df["Industry"] = [industry_dict.get(stock, "Unknown") for stock in centrality_df.index]
industry_centrality = centrality_df.groupby("Industry").mean()
industry_centrality = industry_centrality[["Weighted_Degree", "Degree", "Betweenness", "Closeness", "Eigenvector"]]
print("\nĐộ trung tâm trung bình theo ngành:")
print(industry_centrality.sort_values(by="Weighted_Degree", ascending=False))

# Lưu vào file
industry_centrality.to_csv("industry_centrality.csv")

# d. Đánh giá thống kê của độ trung tâm
stats = centrality_df[["Degree", "Weighted_Degree", "Betweenness", "Closeness", "Eigenvector"]].describe()
print("\nThống kê mô tả của các thước đo độ trung tâm:")
print(stats)
stats.to_csv("centrality_stats.csv")

# e. Code mẫu cho hồi quy (nếu có dữ liệu tài chính sau này)
financials_file = "financial_indicators.csv"
if os.path.exists(financials_file):
    import statsmodels.api as sm
    financials = pd.read_csv(financials_file, index_col="Stock")
    if "Vlat" not in financials.columns:
        financials["Vlat"] = returns.std() * np.sqrt(252)
    reg_results = {}
    X = financials[["ROA", "Lev", "Vl", "MC", "Vlat"]]
    X = sm.add_constant(X)
    for centrality in ["Weighted_Degree", "Degree", "Betweenness", "Closeness", "Eigenvector"]:
        y = centrality_df[centrality]
        common_index = X.index.intersection(y.index)
        X_reg = X.loc[common_index]
        y_reg = y.loc[common_index]
        model = sm.OLS(y_reg, X_reg).fit()
        reg_results[centrality] = model.summary2().tables[1]
        print(f"\nHồi quy cho {centrality}:\n{model.summary()}")
    with open("regression_results.txt", "w") as f:
        for centrality, result in reg_results.items():
            f.write(f"\nHồi quy cho {centrality}:\n{result}\n")
else:
    print("\nKhông có dữ liệu tài chính, bỏ qua hồi quy.")

# Thêm hàm vẽ mạng lưới
def visualize_network(G, centrality_df, output_file="network_visualization.png"):
    """
    Vẽ mạng lưới với các nút được tô màu theo ngành và kích thước theo độ trung tâm
    
    Args:
        G (nx.Graph): Đồ thị mạng lưới
        centrality_df (pd.DataFrame): DataFrame chứa các thước đo độ trung tâm
        output_file (str): Tên file để lưu hình ảnh
    """
    plt.figure(figsize=(20, 16))
    
    # Tạo bản đồ màu cho các ngành
    industries = centrality_df['Industry'].unique()
    color_map = plt.cm.get_cmap('tab20', len(industries))
    industry_colors = {industry: color_map(i) for i, industry in enumerate(industries)}
    
    # Tạo danh sách màu cho các nút
    node_colors = [industry_colors[centrality_df.loc[node, 'Industry']] for node in G.nodes()]
    
    # Kích thước nút dựa trên Weighted Degree
    node_sizes = [centrality_df.loc[node, 'Weighted_Degree'] * 500 for node in G.nodes()]
    
    # Tạo layout cho đồ thị
    pos = nx.spring_layout(G, k=0.15, iterations=50, seed=42)
    
    # Vẽ các cạnh với độ trong suốt dựa trên trọng số
    edge_weights = [G[u][v]['weight'] for u, v in G.edges()]
    max_weight = max(edge_weights)
    edge_alphas = [0.2 + 0.8 * (weight / max_weight) for weight in edge_weights]
    
    # Vẽ các cạnh
    edges = nx.draw_networkx_edges(
        G, pos, 
        alpha=0.3,
        width=[w * 2 for w in edge_weights],
        edge_color='gray'
    )
    
    # Vẽ các nút
    nodes = nx.draw_networkx_nodes(
        G, pos,
        node_color=node_colors,
        node_size=node_sizes,
        alpha=0.8
    )
    
    # Thêm chú thích cho các ngành
    legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                                 markerfacecolor=color, markersize=10, 
                                 label=industry) 
                      for industry, color in industry_colors.items()]
    plt.legend(handles=legend_elements, loc='upper right', title='Ngành')
    
    # Thêm tiêu đề và thông tin
    plt.title(f'Mạng lưới tương quan cổ phiếu (Ngưỡng θ = {theta})', fontsize=16)
    plt.text(0.01, 0.01, f'Số nút: {G.number_of_nodes()}, Số cạnh: {G.number_of_edges()}', 
             transform=plt.gca().transAxes, fontsize=12)
    
    # Tắt trục
    plt.axis('off')
    
    # Lưu hình ảnh
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Đã lưu hình ảnh mạng lưới vào file '{output_file}'")
    plt.show()

# Thêm hàm vẽ mạng lưới tương tác
def visualize_interactive_network(G, centrality_df, output_file="interactive_network.html"):
    """
    Tạo biểu đồ mạng lưới tương tác sử dụng Pyvis
    
    Args:
        G (nx.Graph): Đồ thị mạng lưới
        centrality_df (pd.DataFrame): DataFrame chứa các thước đo độ trung tâm
        output_file (str): Tên file HTML để lưu biểu đồ tương tác
    """
    try:
        from pyvis.network import Network
        
        # Tạo mạng lưới tương tác
        net = Network(height="800px", width="100%", notebook=False, directed=False)
        
        # Tạo bản đồ màu cho các ngành
        industries = centrality_df['Industry'].unique()
        color_map = plt.cm.get_cmap('tab20', len(industries))
        industry_colors = {industry: f'rgb{tuple(int(255*x) for x in color_map(i)[:3])}' 
                          for i, industry in enumerate(industries)}
        
        # Thêm các nút vào mạng lưới
        for node in G.nodes():
            industry = centrality_df.loc[node, 'Industry']
            weighted_degree = centrality_df.loc[node, 'Weighted_Degree']
            
            # Tạo thông tin chi tiết cho nút
            title = f"<b>{node}</b><br>Ngành: {industry}<br>Weighted Degree: {weighted_degree:.4f}"
            
            # Thêm nút với kích thước dựa trên Weighted Degree
            net.add_node(
                node, 
                label=node, 
                title=title,
                color=industry_colors[industry],
                size=10 + 40 * weighted_degree
            )
        
        # Thêm các cạnh vào mạng lưới
        for u, v, data in G.edges(data=True):
            weight = data['weight']
            # Chỉ hiển thị cạnh có trọng số lớn hơn ngưỡng để giảm độ phức tạp
            if weight > theta * 1.2:  # Tăng ngưỡng để giảm số lượng cạnh hiển thị
                net.add_edge(u, v, value=weight*10, title=f"Tương quan: {weight:.4f}")
        
        # Cấu hình tùy chọn vật lý
        net.barnes_hut(gravity=-10000, central_gravity=0.3, spring_length=200, spring_strength=0.05)
        
        # Thêm chú thích
        net.set_options("""
        var options = {
          "nodes": {
            "borderWidth": 2,
            "borderWidthSelected": 4,
            "font": {
              "size": 15
            }
          },
          "edges": {
            "color": {
              "opacity": 0.5
            },
            "smooth": {
              "type": "continuous",
              "forceDirection": "none"
            }
          },
          "physics": {
            "stabilization": {
              "iterations": 100
            }
          }
        }
        """)
        
        # Lưu mạng lưới tương tác vào file HTML
        net.save_graph(output_file)
        print(f"Đã lưu biểu đồ mạng lưới tương tác vào file '{output_file}'")
        
    except ImportError:
        print("Thư viện pyvis không được cài đặt. Vui lòng cài đặt bằng lệnh: pip install pyvis")

# Vẽ mạng lưới
print("\nĐang vẽ mạng lưới...")
visualize_network(G, centrality_df, output_file="network_visualization.png")

# Vẽ mạng lưới tương tác (nếu có thư viện pyvis)
# try:
#     visualize_interactive_network(G, centrality_df, output_file="interactive_network.html")
# except Exception as e:
#     print(f"Không thể tạo biểu đồ tương tác: {str(e)}")
#     print("Để sử dụng biểu đồ tương tác, hãy cài đặt thư viện pyvis: pip install pyvis")

print("\nĐã hoàn thành các đánh giá và lưu kết quả.")
