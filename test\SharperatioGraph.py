import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt

# ---- 1. <PERSON><PERSON><PERSON> file log return ----
returns = pd.read_csv('returns.csv')
returns = returns.set_index('Date') if 'Date' in returns.columns else returns

# ---- 2. Tính Sharpe ratio ----
risk_free_rate = 0.05 / 252  # Lãi suất phi rủi ro hàng ngày (5% năm)
mean_returns = returns.mean()
std_returns = returns.std()
sharpe_ratios = (mean_returns - risk_free_rate) / std_returns

sharpe_df = pd.DataFrame({
    'stock': sharpe_ratios.index,
    'sharpe_ratio': sharpe_ratios.values
})

# ---- 3. Đ<PERSON>c file centrality ----
centrality_df = pd.read_csv('centrality_measures.csv')
centrality_df = centrality_df.rename(columns={centrality_df.columns[0]: 'stock'})  # cột đầu là 'stock'

# ---- 4. <PERSON><PERSON><PERSON> dữ liệu ----
df = pd.merge(sharpe_df, centrality_df, on='stock')

# ---- 5. <PERSON><PERSON> nhóm theo centrality ----
# Bạn có thể chọn một trong các độ đo trung tâm: 'Degree', 'Weighted_Degree', 'Betweenness', 'Closeness', 'Eigenvector'
centrality_metric = 'Weighted_Degree'

# Phân nhóm thành 3 terciles (low, middle, high)
df['centrality_group'] = pd.qcut(df[centrality_metric], q=3, labels=['Low', 'Middle', 'High'])

# ---- 6. Vẽ biểu đồ boxplot ----
plt.figure(figsize=(10, 6))
sns.boxplot(data=df, x='centrality_group', y='sharpe_ratio', palette='viridis')

plt.title(f'Sharpe Ratio Distribution for Low, Middle and High Terciles of {centrality_metric}', fontsize=14)
plt.xlabel('Centrality Group', fontsize=12)
plt.ylabel('Sharpe Ratio', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()

# Lưu biểu đồ
plt.savefig(f'sharpe_ratio_by_{centrality_metric.lower()}.png', dpi=300, bbox_inches='tight')
plt.show()

# ---- 7. Vẽ biểu đồ violin plot ----
plt.figure(figsize=(10, 6))
sns.violinplot(data=df, x='centrality_group', y='sharpe_ratio', palette='viridis', inner='quartile')

plt.title(f'Sharpe Ratio Distribution for Low, Middle and High Terciles of {centrality_metric}', fontsize=14)
plt.xlabel('Centrality Group', fontsize=12)
plt.ylabel('Sharpe Ratio', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()

# Lưu biểu đồ
plt.savefig(f'sharpe_ratio_violin_by_{centrality_metric.lower()}.png', dpi=300, bbox_inches='tight')
plt.show()

# ---- 8. Vẽ biểu đồ scatter plot ----
plt.figure(figsize=(10, 6))
sns.scatterplot(data=df, x=centrality_metric, y='sharpe_ratio', hue='centrality_group', palette='viridis', s=80, alpha=0.7)

# Thêm đường hồi quy
sns.regplot(data=df, x=centrality_metric, y='sharpe_ratio', scatter=False, color='red', line_kws={'linewidth': 2})

# Thêm nhãn cho các điểm nổi bật
top_stocks = df.nlargest(5, 'sharpe_ratio')
bottom_stocks = df.nsmallest(5, 'sharpe_ratio')
highlight_stocks = pd.concat([top_stocks, bottom_stocks])

for idx, row in highlight_stocks.iterrows():
    plt.text(row[centrality_metric], row['sharpe_ratio'], row['stock'], 
             fontsize=9, ha='center', va='center',
             bbox=dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.3'))

plt.title(f'Relationship between {centrality_metric} and Sharpe Ratio', fontsize=14)
plt.xlabel(centrality_metric, fontsize=12)
plt.ylabel('Sharpe Ratio', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.5)
plt.tight_layout()

# Lưu biểu đồ
plt.savefig(f'sharpe_ratio_scatter_by_{centrality_metric.lower()}.png', dpi=300, bbox_inches='tight')
plt.show()

# ---- 9. Tính toán thống kê mô tả ----
stats = df.groupby('centrality_group')['sharpe_ratio'].describe()
print("\nThống kê Sharpe ratio theo nhóm centrality:")
print(stats)

# Tính tương quan giữa Sharpe ratio và các độ đo trung tâm
corr = df[['sharpe_ratio', 'Degree', 'Weighted_Degree', 'Betweenness', 'Closeness', 'Eigenvector']].corr()
print("\nTương quan giữa Sharpe ratio và các độ đo trung tâm:")
print(corr['sharpe_ratio'])

# ---- 10. Vẽ biểu đồ so sánh tất cả các độ đo trung tâm ----
plt.figure(figsize=(15, 10))

# Tạo 5 subplots cho 5 độ đo trung tâm
centrality_measures = ['Degree', 'Weighted_Degree', 'Betweenness', 'Closeness', 'Eigenvector']
for i, measure in enumerate(centrality_measures, 1):
    plt.subplot(2, 3, i)
    
    # Phân nhóm theo độ đo trung tâm hiện tại
    temp_df = df.copy()
    temp_df['group'] = pd.qcut(temp_df[measure], q=3, labels=['Low', 'Middle', 'High'])
    
    # Vẽ boxplot
    sns.boxplot(data=temp_df, x='group', y='sharpe_ratio', palette='viridis')
    
    plt.title(f'{measure}', fontsize=12)
    plt.xlabel('Centrality Group', fontsize=10)
    plt.ylabel('Sharpe Ratio', fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.5)

plt.suptitle('Sharpe Ratio Distribution by Different Centrality Measures', fontsize=16)
plt.tight_layout()
plt.subplots_adjust(top=0.9)

# Lưu biểu đồ
plt.savefig('sharpe_ratio_by_all_centrality_measures.png', dpi=300, bbox_inches='tight')
plt.show()

# ---- 11. Vẽ biểu đồ heatmap tương quan ----
plt.figure(figsize=(10, 8))
correlation_subset = df[['sharpe_ratio', 'Degree', 'Weighted_Degree', 'Betweenness', 'Closeness', 'Eigenvector']].corr()
mask = np.triu(np.ones_like(correlation_subset, dtype=bool))
sns.heatmap(correlation_subset, annot=True, fmt=".2f", cmap="coolwarm", mask=mask, 
            vmin=-1, vmax=1, center=0, square=True, linewidths=.5)

plt.title('Correlation between Sharpe Ratio and Centrality Measures', fontsize=14)
plt.tight_layout()

# Lưu biểu đồ
plt.savefig('sharpe_ratio_centrality_correlation.png', dpi=300, bbox_inches='tight')
plt.show()
