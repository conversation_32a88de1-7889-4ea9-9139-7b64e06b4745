import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from collections import Counter
from scipy.stats import linregress, skew, kurtosis
import pandas as pd

def create_hvg(time_series):
    """
    Tạo Horizontal Visibility Graph (HVG) từ chuỗi thời gian.
    Args:
        time_series (list or np.array): Chuỗi thời gian (giá đóng cửa hoặc tỷ suất sinh lợi).
    Returns:
        G (nx.Graph): Đồ thị HVG.
    """
    n = len(time_series)
    G = nx.Graph()
    
    # Thêm các nút tương ứng với mỗi điểm trong chuỗi thời gian
    for i in range(n):
        G.add_node(i, value=time_series[i])
    
    # Kiểm tra điều kiện "nhìn thấy" và thêm cạnh
    for i in range(n):
        for j in range(i + 1, n):
            # Kiểm tra xem hai điểm i và j có "nhìn thấy" nhau không
            visible = True
            for k in range(i + 1, j):
                if time_series[k] >= min(time_series[i], time_series[j]):
                    visible = False
                    break
            if visible:
                G.add_edge(i, j)
    
    return G

def degree_distribution(G):
    """
    Tính phân phối bậc của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        dict: Từ điển chứa bậc và số lượng nút có bậc đó.
    """
    degrees = [degree for node, degree in G.degree()]
    degree_counts = Counter(degrees)
    return dict(degree_counts)

def average_degree(G):
    """
    Tính độ liên kết trung bình của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        float: Độ liên kết trung bình.
    """
    degrees = [degree for node, degree in G.degree()]
    return np.mean(degrees) if degrees else 0

def clustering_coefficient(G):
    """
    Tính hệ số cụm trung bình của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        float: Hệ số cụm trung bình.
    """
    return nx.average_clustering(G)

def calculate_centrality_metrics(G):
    """
    Tính toán các độ đo trung tâm của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG
    Returns:
        dict: Các độ đo trung tâm
    """
    metrics = {}
    
    # Tính closeness centrality
    close_cent = nx.closeness_centrality(G)
    metrics['closeness'] = {
        'values': close_cent,
        'mean': np.mean(list(close_cent.values())),
        'std': np.std(list(close_cent.values())),
        'max': max(close_cent.values()),
        'min': min(close_cent.values()),
        'top_nodes': sorted(close_cent.items(), key=lambda x: x[1], reverse=True)[:5]
    }
    
    # Tính betweenness centrality
    between_cent = nx.betweenness_centrality(G)
    metrics['betweenness'] = {
        'values': between_cent,
        'mean': np.mean(list(between_cent.values())),
        'std': np.std(list(between_cent.values())),
        'max': max(between_cent.values()),
        'min': min(between_cent.values()),
        'top_nodes': sorted(between_cent.items(), key=lambda x: x[1], reverse=True)[:5]
    }
    
    return metrics

def plot_centrality_distribution(G, metrics):
    """
    Vẽ biểu đồ phân phối của các độ đo trung tâm.
    Args:
        G (nx.Graph): Đồ thị HVG
        metrics (dict): Các độ đo trung tâm
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Phân phối closeness centrality
    close_values = list(metrics['closeness']['values'].values())
    ax1.hist(close_values, bins=30, alpha=0.7, color='blue')
    ax1.axvline(metrics['closeness']['mean'], color='r', linestyle='dashed',
                label=f'Mean: {metrics["closeness"]["mean"]:.4f}')
    ax1.set_xlabel('Closeness Centrality')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Closeness Centrality Distribution')
    ax1.legend()
    
    # Phân phối betweenness centrality
    between_values = list(metrics['betweenness']['values'].values())
    ax2.hist(between_values, bins=30, alpha=0.7, color='green')
    ax2.axvline(metrics['betweenness']['mean'], color='r', linestyle='dashed',
                label=f'Mean: {metrics["betweenness"]["mean"]:.4f}')
    ax2.set_xlabel('Betweenness Centrality')
    ax2.set_ylabel('Frequency')
    ax2.set_title('Betweenness Centrality Distribution')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('centrality_distributions.png')
    plt.close()

def plot_hvg_and_timeseries(time_series, G, title="Horizontal Visibility Graph and Time Series"):
    """
    Vẽ chuỗi thời gian và đồ thị HVG với nhãn cho các nút.
    Args:
        time_series (list or np.array): Chuỗi thời gian.
        G (nx.Graph): Đồ thị HVG.
        title (str): Tiêu đề của biểu đồ.
    """
    # Tạo figure với 2 subplot
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    
    # Vẽ chuỗi thời gian
    ax1.plot(range(len(time_series)), time_series, marker='o', linestyle='-', color='b')
    ax1.set_title("Time Series")
    ax1.set_xlabel("Time")
    ax1.set_ylabel("Value")
    ax1.grid(True)
    
    # Vẽ đồ thị HVG
    pos = {i: (i, time_series[i]) for i in range(len(time_series))}
    nx.draw(G, pos, ax=ax2, node_size=50, node_color='r', edge_color='gray')
    # Thêm nhãn cho các nút
    labels = {i: str(i) for i in range(len(time_series))}
    nx.draw_networkx_labels(G, pos, labels, font_size=10, font_color='black', ax=ax2)
    ax2.set_title("Horizontal Visibility Graph")
    ax2.set_xlabel("Time")
    ax2.set_ylabel("Value")
    
    plt.tight_layout()
    plt.savefig('hvg_and_timeseries.png')
    plt.close()

def plot_degree_distribution(degree_dist):
    """
    Vẽ phân phối bậc của đồ thị.
    Args:
        degree_dist (dict): Phân phối bậc.
    """
    degrees = list(degree_dist.keys())
    counts = list(degree_dist.values())
    
    plt.figure(figsize=(8, 6))
    plt.bar(degrees, counts, color='green')
    plt.title("Degree Distribution")
    plt.xlabel("Degree")
    plt.ylabel("Frequency")
    plt.grid(True)
    plt.savefig('degree_distribution.png')
    plt.close()

def get_adjacency_matrix(G):
    """
    Tính ma trận kề của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        np.array: Ma trận kề.
    """
    return nx.adjacency_matrix(G).toarray()

def calculate_network_density(G):
    """
    Tính mật độ mạng.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        float: Mật độ mạng
    """
    return nx.density(G)

def find_shortest_paths(G):
    """
    Tìm đường đi ngắn nhất giữa tất cả các cặp đỉnh.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        dict: Dictionary chứa độ dài đường đi ngắn nhất.
    """
    return dict(nx.shortest_path_length(G))

def has_euler_path(G):
    """
    Kiểm tra xem đồ thị có đường đi Euler không.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        bool: True nếu có đường đi Euler.
    """
    # Đồ thị có đường đi Euler nếu có 0 hoặc 2 đỉnh bậc lẻ
    odd_degree_count = sum(1 for _, degree in G.degree() if degree % 2 == 1)
    return odd_degree_count in [0, 2]

def has_hamilton_path(G):
    """
    Kiểm tra xem đồ thị có đường đi Hamilton không.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        bool: True nếu có đường đi Hamilton.
    """
    n = G.number_of_nodes()
    if n < 3:
        return True
    # Điều kiện Dirac: nếu bậc của mỗi đỉnh ≥ n/2
    min_degree = min(dict(G.degree()).values())
    return min_degree >= n/2

def count_components(G):
    """
    Đếm số thành phần liên thông.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        int: Số thành phần liên thông.
    """
    return nx.number_connected_components(G)

def find_cut_sets(G):
    """
    Tìm các tập cắt của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        list: Danh sách các tập cắt.
    """
    cut_sets = []
    # Tìm các đỉnh cắt
    cut_vertices = list(nx.articulation_points(G))
    if cut_vertices:
        cut_sets.append(("Articulation Points", cut_vertices))
    
    # Tìm các cạnh cắt
    bridges = list(nx.bridges(G))
    if bridges:
        cut_sets.append(("Bridges", bridges))
    
    return cut_sets

def calculate_network_metrics(G):
    """
    Tính toán các thông số đo lường mạng.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        dict: Các thông số đo lường mạng.
    """
    metrics = {}
    
    # 1. Cliques và k-cores
    try:
        # Số lượng cliques tối đa
        metrics['max_clique_size'] = len(max(nx.find_cliques(G), key=len))
        # k-core số lớn nhất
        metrics['max_core_number'] = max(nx.core_number(G).values())
    except:
        metrics['max_clique_size'] = 0
        metrics['max_core_number'] = 0

    # 2. k-components
    try:
        # Số thành phần liên thông mạnh nhất
        metrics['k_components'] = len(list(nx.k_components(G)))
    except:
        metrics['k_components'] = 0

    # 3. Redundancy
    try:
        # Tính redundancy như tỷ lệ giữa số cạnh thực tế và số cạnh tối đa có thể
        n = G.number_of_nodes()
        max_edges = n * (n-1) / 2
        actual_edges = G.number_of_edges()
        metrics['redundancy'] = actual_edges / max_edges if max_edges > 0 else 0
    except:
        metrics['redundancy'] = 0

    # 4. Reciprocity (cho đồ thị có hướng)
    try:
        D = G.to_directed()
        metrics['reciprocity'] = nx.reciprocity(D)
    except:
        metrics['reciprocity'] = 0

    # 5. Similarity
    try:
        # Jaccard similarity giữa các nút
        similarity_matrix = np.zeros((G.number_of_nodes(), G.number_of_nodes()))
        for i in G.nodes():
            for j in G.nodes():
                neighbors_i = set(G.neighbors(i))
                neighbors_j = set(G.neighbors(j))
                if len(neighbors_i | neighbors_j) > 0:
                    similarity_matrix[i][j] = len(neighbors_i & neighbors_j) / len(neighbors_i | neighbors_j)
        metrics['avg_similarity'] = np.mean(similarity_matrix)
    except:
        metrics['avg_similarity'] = 0

    # 6. Pearson correlation
    try:
        # Tính hệ số tương quan Pearson giữa các bậc của các nút kề nhau
        degree_seq = [d for n, d in G.degree()]
        metrics['degree_pearson_correlation'] = nx.degree_pearson_correlation_coefficient(G)
    except:
        metrics['degree_pearson_correlation'] = 0

    return metrics

def plot_shortest_paths_distribution(G):
    """
    Vẽ và phân tích phân bố đường đi ngắn nhất trong đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG.
    Returns:
        dict: Các thống kê về đường đi ngắn nhất
    """
    # Tính toán tất cả các đường đi ngắn nhất
    shortest_paths = dict(nx.shortest_path_length(G))
    
    # Tạo danh sách độ dài đường đi
    path_lengths = []
    for source in shortest_paths:
        path_lengths.extend(shortest_paths[source].values())
    
    # Tính các thống kê
    stats = {
        'average_path': np.mean(path_lengths),
        'max_path': max(path_lengths),
        'min_path': min(path_lengths),
        'median_path': np.median(path_lengths),
        'std_path': np.std(path_lengths)
    }
    
    # Tính phân phối
    path_counts = Counter(path_lengths)
    
    # Vẽ histogram
    plt.figure(figsize=(10, 6))
    plt.hist(path_lengths, bins=max(path_lengths)-min(path_lengths)+1, 
             density=True, alpha=0.7, color='blue')
    plt.title("Distribution of Shortest Path Lengths")
    plt.xlabel("Path Length")
    plt.ylabel("Frequency")
    plt.grid(True)
    
    # Thêm đường trung bình và trung vị
    plt.axvline(stats['average_path'], color='r', linestyle='dashed', 
                label=f'Mean: {stats["average_path"]:.2f}')
    plt.axvline(stats['median_path'], color='g', linestyle='dashed', 
                label=f'Median: {stats["median_path"]:.2f}')
    plt.legend()
    
    plt.savefig('shortest_paths_distribution.png')
    plt.close()
    
    return stats

def analyze_shortest_paths(G, stats):
    """
    Phân tích và đánh giá thống kê đường đi ngắn nhất.
    Args:
        G (nx.Graph): Đồ thị HVG
        stats (dict): Các thống kê về đường đi ngắn nhất
    """
    print("\nShortest Paths Analysis:")
    print(f"  Average Path Length: {stats['average_path']:.2f}")
    print(f"  Maximum Path Length: {stats['max_path']}")
    print(f"  Minimum Path Length: {stats['min_path']}")
    print(f"  Median Path Length: {stats['median_path']:.2f}")
    print(f"  Standard Deviation: {stats['std_path']:.2f}")
    
    # Đánh giá
    print("\nInterpretation:")
    
    # Đánh giá độ tập trung
    if stats['std_path'] < 1:
        print("  - Path lengths are very concentrated, suggesting a uniform network structure")
    elif stats['std_path'] < 2:
        print("  - Path lengths show moderate variation, indicating balanced connectivity")
    else:
        print("  - High variation in path lengths, suggesting diverse network regions")
    
    # Đánh giá hiệu quả
    if stats['average_path'] < np.log(G.number_of_nodes()):
        print("  - Network shows efficient connectivity (shorter than logarithmic scaling)")
    else:
        print("  - Network shows typical path lengths (following logarithmic scaling)")
    
    # Đánh giá cấu trúc
    if stats['max_path'] > 2 * stats['average_path']:
        print("  - Network has some elongated structures or sparse regions")
    else:
        print("  - Network has relatively compact structure")

def plot_centrality_scatter(G):
    """
    Vẽ biểu đồ phân tán so sánh các độ đo trung tâm khác nhau.
    Args:
        G (nx.Graph): Đồ thị HVG
    """
    # Tính các độ đo trung tâm
    degree_cent = nx.degree_centrality(G)
    betweenness_cent = nx.betweenness_centrality(G)
    closeness_cent = nx.closeness_centrality(G)
    
    # Chuyển đổi sang danh sách để vẽ
    nodes = list(G.nodes())
    degree_values = [degree_cent[n] for n in nodes]
    betweenness_values = [betweenness_cent[n] for n in nodes]
    closeness_values = [closeness_cent[n] for n in nodes]
    
    # Tạo figure với 2 subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Biểu đồ 1: Degree vs Betweenness
    scatter1 = ax1.scatter(degree_values, betweenness_values, 
                          alpha=0.5, c=closeness_values, cmap='viridis')
    ax1.set_xlabel('Degree Centrality')
    ax1.set_ylabel('Betweenness Centrality')
    ax1.set_title('Degree vs Betweenness Centrality')
    fig.colorbar(scatter1, ax=ax1, label='Closeness Centrality')
    
    # Thêm đường hồi quy
    z1 = np.polyfit(degree_values, betweenness_values, 1)
    p1 = np.poly1d(z1)
    ax1.plot(degree_values, p1(degree_values), "r--", alpha=0.8, 
             label=f'Trend line (r={np.corrcoef(degree_values, betweenness_values)[0,1]:.2f})')
    ax1.legend()
    
    # Biểu đồ 2: Degree vs Closeness
    scatter2 = ax2.scatter(degree_values, closeness_values, 
                          alpha=0.5, c=betweenness_values, cmap='viridis')
    ax2.set_xlabel('Degree Centrality')
    ax2.set_ylabel('Closeness Centrality')
    ax2.set_title('Degree vs Closeness Centrality')
    fig.colorbar(scatter2, ax=ax2, label='Betweenness Centrality')
    
    # Thêm đường hồi quy
    z2 = np.polyfit(degree_values, closeness_values, 1)
    p2 = np.poly1d(z2)
    ax2.plot(degree_values, p2(degree_values), "r--", alpha=0.8, 
             label=f'Trend line (r={np.corrcoef(degree_values, closeness_values)[0,1]:.2f})')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('centrality_scatter.png')
    plt.close()
    
    # In phân tích thống kê
    print("\nCentrality Analysis:")
    print(f"Correlation between Degree and Betweenness: {np.corrcoef(degree_values, betweenness_values)[0,1]:.4f}")
    print(f"Correlation between Degree and Closeness: {np.corrcoef(degree_values, closeness_values)[0,1]:.4f}")
    print(f"Correlation between Betweenness and Closeness: {np.corrcoef(betweenness_values, closeness_values)[0,1]:.4f}")

def plot_eigenvector_centrality(G):
    """
    Vẽ biểu đồ phân tán vector riêng trung tâm.
    Args:
        G (nx.Graph): Đồ thị HVG
    """
    try:
        # Tính vector riêng trung tâm
        eig_cent = nx.eigenvector_centrality(G)
        degree_cent = nx.degree_centrality(G)
        
        # Chuyển đổi sang danh sách
        nodes = list(G.nodes())
        eig_values = [eig_cent[n] for n in nodes]
        degree_values = [degree_cent[n] for n in nodes]
        
        # Tạo biểu đồ
        plt.figure(figsize=(10, 6))
        plt.scatter(degree_values, eig_values, alpha=0.5)
        plt.xlabel('Degree Centrality')
        plt.ylabel('Eigenvector Centrality')
        plt.title('Degree vs Eigenvector Centrality')
        
        # Thêm đường hồi quy
        z = np.polyfit(degree_values, eig_values, 1)
        p = np.poly1d(z)
        plt.plot(degree_values, p(degree_values), "r--", alpha=0.8,
                label=f'Trend line (r={np.corrcoef(degree_values, eig_values)[0,1]:.2f})')
        plt.legend()
        
        plt.grid(True)
        plt.savefig('eigenvector_centrality.png')
        plt.close()
        
        # In phân tích
        print("\nEigenvector Centrality Analysis:")
        print(f"Correlation with Degree Centrality: {np.corrcoef(degree_values, eig_values)[0,1]:.4f}")
        print(f"Maximum Eigenvector Centrality: {max(eig_values):.4f}")
        print(f"Minimum Eigenvector Centrality: {min(eig_values):.4f}")
        print(f"Average Eigenvector Centrality: {np.mean(eig_values):.4f}")
        
    except nx.PowerIterationFailedConvergence:
        print("Warning: Eigenvector centrality calculation did not converge")

def plot_katz_centrality(G):
    """
    Vẽ biểu đồ phân tán Katz centrality và so sánh với các độ đo trung tâm khác.
    Args:
        G (nx.Graph): Đồ thị HVG
    """
    try:
        # Tính các độ đo trung tâm
        katz_cent = nx.katz_centrality_numpy(G)  # Sử dụng numpy để tính toán ổn định hơn
        degree_cent = nx.degree_centrality(G)
        eigenvector_cent = nx.eigenvector_centrality(G)
        
        # Chuyển đổi sang danh sách
        nodes = list(G.nodes())
        katz_values = [katz_cent[n] for n in nodes]
        degree_values = [degree_cent[n] for n in nodes]
        eigenvector_values = [eigenvector_cent[n] for n in nodes]
        
        # Tạo figure với 2 subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Biểu đồ 1: Katz vs Degree Centrality
        scatter1 = ax1.scatter(degree_values, katz_values, 
                             alpha=0.6, c=eigenvector_values, cmap='viridis')
        ax1.set_xlabel('Degree Centrality')
        ax1.set_ylabel('Katz Centrality')
        ax1.set_title('Katz vs Degree Centrality')
        fig.colorbar(scatter1, ax=ax1, label='Eigenvector Centrality')
        
        # Thêm đường hồi quy
        z1 = np.polyfit(degree_values, katz_values, 1)
        p1 = np.poly1d(z1)
        ax1.plot(degree_values, p1(degree_values), "r--", alpha=0.8,
                label=f'Trend line (r={np.corrcoef(degree_values, katz_values)[0,1]:.2f})')
        ax1.legend()
        
        # Biểu đồ 2: Katz vs Eigenvector Centrality
        scatter2 = ax2.scatter(eigenvector_values, katz_values,
                             alpha=0.6, c=degree_values, cmap='viridis')
        ax2.set_xlabel('Eigenvector Centrality')
        ax2.set_ylabel('Katz Centrality')
        ax2.set_title('Katz vs Eigenvector Centrality')
        fig.colorbar(scatter2, ax=ax2, label='Degree Centrality')
        
        # Thêm đường hồi quy
        z2 = np.polyfit(eigenvector_values, katz_values, 1)
        p2 = np.poly1d(z2)
        ax2.plot(eigenvector_values, p2(eigenvector_values), "r--", alpha=0.8,
                label=f'Trend line (r={np.corrcoef(eigenvector_values, katz_values)[0,1]:.2f})')
        ax2.legend()
        
        plt.tight_layout()
        plt.savefig('katz_centrality_comparison.png')
        plt.close()
        
        # In phân tích thống kê
        print("\nKatz Centrality Analysis:")
        print(f"Correlation with Degree Centrality: {np.corrcoef(degree_values, katz_values)[0,1]:.4f}")
        print(f"Correlation with Eigenvector Centrality: {np.corrcoef(eigenvector_values, katz_values)[0,1]:.4f}")
        print(f"Maximum Katz Centrality: {max(katz_values):.4f}")
        print(f"Minimum Katz Centrality: {min(katz_values):.4f}")
        print(f"Average Katz Centrality: {np.mean(katz_values):.4f}")
        print(f"Standard Deviation: {np.std(katz_values):.4f}")
        
        # Thêm phân tích phân phối
        plt.figure(figsize=(8, 6))
        plt.hist(katz_values, bins=30, alpha=0.7, color='blue')
        plt.axvline(np.mean(katz_values), color='r', linestyle='dashed', 
                   label=f'Mean: {np.mean(katz_values):.4f}')
        plt.axvline(np.median(katz_values), color='g', linestyle='dashed', 
                   label=f'Median: {np.median(katz_values):.4f}')
        plt.xlabel('Katz Centrality')
        plt.ylabel('Frequency')
        plt.title('Distribution of Katz Centrality')
        plt.legend()
        plt.grid(True)
        plt.savefig('katz_centrality_distribution.png')
        plt.close()
        
        # Phân tích thêm về phân phối
        print("\nKatz Centrality Distribution Analysis:")
        print(f"Skewness: {skew(katz_values):.4f}")
        print(f"Kurtosis: {kurtosis(katz_values):.4f}")
        
        # Xác định các node quan trọng nhất
        top_nodes = sorted([(n, v) for n, v in katz_cent.items()], 
                         key=lambda x: x[1], reverse=True)[:5]
        print("\nTop 5 nodes by Katz Centrality:")
        for node, value in top_nodes:
            print(f"Node {node}: {value:.4f}")
            
    except Exception as e:
        print(f"Warning: Katz centrality calculation failed - {str(e)}")

def plot_node_katz_scatter(G):
    """
    Vẽ biểu đồ phân tán giữa node và Katz centrality.
    Args:
        G (nx.Graph): Đồ thị HVG
    """
    try:
        # Tính Katz centrality
        katz_cent = nx.katz_centrality_numpy(G)
        
        # Chuyển đổi sang danh sách
        nodes = list(G.nodes())
        katz_values = [katz_cent[n] for n in nodes]
        
        # Tạo figure
        plt.figure(figsize=(12, 6))
        
        # Vẽ biểu đồ phân tán
        plt.scatter(nodes, katz_values, alpha=0.6, c='blue')
        
        # Thêm đường xu hướng
        z = np.polyfit(nodes, katz_values, 1)
        p = np.poly1d(z)
        plt.plot(nodes, p(nodes), "r--", alpha=0.8,
                label=f'Trend line')
        
        # Tính moving average để thấy xu hướng rõ hơn
        window = min(20, len(nodes)//5)  # Cửa sổ moving average
        if window > 0:
            moving_avg = pd.Series(katz_values).rolling(window=window).mean()
            plt.plot(nodes, moving_avg, 'g-', alpha=0.8, 
                    label=f'Moving average (window={window})')
        
        # Đánh dấu các node có Katz centrality cao nhất
        top_n = 5  # Số node cao nhất muốn đánh dấu
        top_indices = np.argsort(katz_values)[-top_n:]
        plt.scatter(np.array(nodes)[top_indices], 
                   np.array(katz_values)[top_indices], 
                   c='red', s=100, label='Top nodes')
        
        # Thêm labels và title
        plt.xlabel('Node Index')
        plt.ylabel('Katz Centrality')
        plt.title('Node vs Katz Centrality Distribution')
        plt.legend()
        plt.grid(True)
        
        # Thêm annotations cho top nodes
        for idx in top_indices:
            plt.annotate(f'Node {nodes[idx]}',
                        (nodes[idx], katz_values[idx]),
                        xytext=(10, 10), textcoords='offset points')
        
        plt.tight_layout()
        plt.savefig('node_katz_scatter.png')
        plt.close()
        
        # In phân tích
        print("\nNode-Katz Centrality Analysis:")
        print(f"Number of nodes analyzed: {len(nodes)}")
        print("\nTop {top_n} nodes by Katz centrality:")
        for idx in top_indices[::-1]:  # Đảo ngược để in từ cao xuống thấp
            print(f"Node {nodes[idx]}: {katz_values[idx]:.4f}")
        
        # Phân tích phân bố theo vị trí
        first_third = np.mean(katz_values[:len(nodes)//3])
        middle_third = np.mean(katz_values[len(nodes)//3:2*len(nodes)//3])
        last_third = np.mean(katz_values[2*len(nodes)//3:])
        
        print("\nPosition-based analysis:")
        print(f"Average Katz centrality in first third: {first_third:.4f}")
        print(f"Average Katz centrality in middle third: {middle_third:.4f}")
        print(f"Average Katz centrality in last third: {last_third:.4f}")
        
    except Exception as e:
        print(f"Warning: Node-Katz centrality plot failed - {str(e)}")

def analyze_time_series(returns, series_name="Returns Series"):
    """
    Phân tích chuỗi tỷ suất lợi nhuận bằng HVG và tính toán các đặc trưng.
    Args:
        returns (list or np.array): Chuỗi tỷ suất lợi nhuận.
        series_name (str): Tên của chuỗi thời gian.
    """
    # Tạo HVG
    G = create_hvg(returns)
    
    # Các phân tích cơ bản
    deg_dist = degree_distribution(G)
    avg_deg = average_degree(G)
    clust_coeff = clustering_coefficient(G)
    
    # Tính toán các độ đo trung tâm
    centrality_metrics = calculate_centrality_metrics(G)
    
    # Các phân tích mở rộng
    adj_matrix = get_adjacency_matrix(G)
    density = calculate_network_density(G)
    shortest_paths = find_shortest_paths(G)
    has_euler = has_euler_path(G)
    has_hamilton = has_hamilton_path(G)
    num_components = count_components(G)
    cut_sets = find_cut_sets(G)
    
    # Thêm phân tích metrics mới
    network_metrics = calculate_network_metrics(G)
    
    # Vẽ biểu đồ phân phối centrality
    plot_centrality_distribution(G, centrality_metrics)
    
    # In kết quả cơ bản
    print(f"\nAnalysis of {series_name}:")
    print(f"Number of nodes: {G.number_of_nodes()}")
    print(f"Number of edges: {G.number_of_edges()}")
    print(f"Average Degree: {avg_deg:.4f}")
    print(f"Average Clustering Coefficient: {clust_coeff:.4f}")
    
    print("\nCentrality Analysis:")
    print("\nCloseness Centrality:")
    print(f"  Mean: {centrality_metrics['closeness']['mean']:.4f}")
    print(f"  Std: {centrality_metrics['closeness']['std']:.4f}")
    print(f"  Max: {centrality_metrics['closeness']['max']:.4f}")
    print(f"  Min: {centrality_metrics['closeness']['min']:.4f}")
    print("\nTop 5 nodes by Closeness Centrality:")
    for node, value in centrality_metrics['closeness']['top_nodes']:
        print(f"  Node {node}: {value:.4f}")
    
    print("\nBetweenness Centrality:")
    print(f"  Mean: {centrality_metrics['betweenness']['mean']:.4f}")
    print(f"  Std: {centrality_metrics['betweenness']['std']:.4f}")
    print(f"  Max: {centrality_metrics['betweenness']['max']:.4f}")
    print(f"  Min: {centrality_metrics['betweenness']['min']:.4f}")
    print("\nTop 5 nodes by Betweenness Centrality:")
    for node, value in centrality_metrics['betweenness']['top_nodes']:
        print(f"  Node {node}: {value:.4f}")
    
    print("\nNetwork Analysis:")
    print(f"Network Density: {density:.4f}")
    print(f"Number of Components: {num_components}")
    print(f"Has Euler Path: {has_euler}")
    print(f"Has Hamilton Path: {has_hamilton}")
    
    print("\nNetwork Metrics:")
    print(f"  Max Clique Size: {network_metrics['max_clique_size']}")
    print(f"  Max Core Number: {network_metrics['max_core_number']}")
    print(f"  K-Components: {network_metrics['k_components']}")
    print(f"  Redundancy: {network_metrics['redundancy']:.4f}")
    print(f"  Reciprocity: {network_metrics['reciprocity']:.4f}")
    print(f"  Average Similarity: {network_metrics['avg_similarity']:.4f}")
    print(f"  Degree Pearson Correlation: {network_metrics['degree_pearson_correlation']:.4f}")
    
    # Thêm phân tích nâng cao
    print_advanced_analysis(G)
    
    # Vẽ biểu đồ
    plot_hvg_and_timeseries(returns, G, title=f"HVG and {series_name}")
    plot_degree_distribution(deg_dist)

def analyze_k_core(G):
    """
    Phân tích K-core của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG
    Returns:
        dict: Kết quả phân tích K-core
    """
    k_core_results = {}
    
    # Tính k-core number cho mỗi node
    core_numbers = nx.core_number(G)
    
    # Tìm k-core number lớn nhất
    max_core = max(core_numbers.values())
    
    # Thống kê số lượng node cho mỗi k-core
    core_distribution = {}
    for k in range(max_core + 1):
        nodes_in_k_core = len([n for n, c in core_numbers.items() if c >= k])
        core_distribution[k] = nodes_in_k_core
    
    # Tìm các node trong k-core lớn nhất
    max_core_nodes = [n for n, c in core_numbers.items() if c == max_core]
    
    k_core_results['core_numbers'] = core_numbers
    k_core_results['max_core'] = max_core
    k_core_results['core_distribution'] = core_distribution
    k_core_results['max_core_nodes'] = max_core_nodes
    
    return k_core_results

def analyze_bridges(G):
    """
    Phân tích các cạnh bắc cầu trong đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG
    Returns:
        dict: Kết quả phân tích cạnh bắc cầu
    """
    bridge_results = {}
    
    # Tìm tất cả các cạnh bắc cầu
    bridges = list(nx.bridges(G))
    
    # Tính số lượng thành phần liên thông khi loại bỏ từng cạnh bắc cầu
    bridge_impacts = {}
    for bridge in bridges:
        G_temp = G.copy()
        G_temp.remove_edge(*bridge)
        components = list(nx.connected_components(G_temp))
        bridge_impacts[bridge] = {
            'num_components': len(components),
            'component_sizes': [len(comp) for comp in components]
        }
    
    # Tính tỷ lệ cạnh bắc cầu
    bridge_ratio = len(bridges) / G.number_of_edges()
    
    bridge_results['bridges'] = bridges
    bridge_results['bridge_impacts'] = bridge_impacts
    bridge_results['bridge_ratio'] = bridge_ratio
    
    return bridge_results

def analyze_degree_assortativity(G):
    """
    Phân tích assortative mixing theo bậc của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG
    Returns:
        dict: Kết quả phân tích assortative mixing
    """
    assortativity_results = {}
    
    # Tính hệ số assortativity
    assortativity_coef = nx.degree_assortativity_coefficient(G)
    
    # Tính average neighbor degree cho mỗi node
    avg_neighbor_degree = nx.average_neighbor_degree(G)
    
    # Tính correlation giữa node degree và average neighbor degree
    degrees = dict(G.degree())
    degree_neighbor_correlation = np.corrcoef(
        list(degrees.values()),
        list(avg_neighbor_degree.values())
    )[0,1]
    
    # Phân loại mixing pattern
    mixing_pattern = "Assortative" if assortativity_coef > 0.1 else \
                    "Disassortative" if assortativity_coef < -0.1 else \
                    "Neutral"
    
    assortativity_results['assortativity_coefficient'] = assortativity_coef
    assortativity_results['avg_neighbor_degree'] = avg_neighbor_degree
    assortativity_results['degree_neighbor_correlation'] = degree_neighbor_correlation
    assortativity_results['mixing_pattern'] = mixing_pattern
    
    return assortativity_results

def print_advanced_analysis(G):
    """
    In kết quả phân tích nâng cao của đồ thị.
    Args:
        G (nx.Graph): Đồ thị HVG
    """
    # Phân tích K-core
    k_core_results = analyze_k_core(G)
    print("\nK-core Analysis:")
    print(f"Maximum k-core number: {k_core_results['max_core']}")
    print("K-core distribution:")
    for k, count in k_core_results['core_distribution'].items():
        print(f"  k={k}: {count} nodes")
    print(f"Nodes in maximum k-core: {k_core_results['max_core_nodes']}")
    
    # Phân tích cạnh bắc cầu
    bridge_results = analyze_bridges(G)
    print("\nBridge Analysis:")
    print(f"Number of bridges: {len(bridge_results['bridges'])}")
    print(f"Bridge ratio: {bridge_results['bridge_ratio']:.4f}")
    print("Top 5 critical bridges (by component impact):")
    sorted_bridges = sorted(
        bridge_results['bridge_impacts'].items(),
        key=lambda x: x[1]['num_components'],
        reverse=True
    )[:5]
    for bridge, impact in sorted_bridges:
        print(f"  Edge {bridge}: splits into {impact['num_components']} components")
        print(f"    Component sizes: {impact['component_sizes']}")
    
    # Phân tích assortative mixing
    assortativity_results = analyze_degree_assortativity(G)
    print("\nAssortative Mixing Analysis:")
    print(f"Degree assortativity coefficient: {assortativity_results['assortativity_coefficient']:.4f}")
    print(f"Degree-neighbor correlation: {assortativity_results['degree_neighbor_correlation']:.4f}")
    print(f"Mixing pattern: {assortativity_results['mixing_pattern']}")

# Ví dụ sử dụng
if __name__ == "__main__":
    # Đọc dữ liệu từ file Excel
    try:
        df = pd.read_excel('ket_qua_tong_hop.xlsx')
        # Lấy dữ liệu từ cột D, từ hàng 3 đến hàng 600
        time_series = df.iloc[2:563, 3].values.tolist()  # Index 3 tương ứng với cột D
        
        # Loại bỏ các giá trị NaN nếu có
        time_series = [x for x in time_series if pd.notna(x)]
        
        # Phân tích chuỗi thời gian
        analyze_time_series(time_series, series_name="Stock Returns Analysis")
    except FileNotFoundError:
        print("Error: File 'ket_qua_tong_hop.xlsx' not found")
    except Exception as e:
        print(f"Error reading Excel file: {str(e)}")
